package com.alipay.codegencore.service.impl.common;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.DocumentDOExample;
import com.alipay.codegencore.dal.mapper.DocumentDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.*;
import com.alipay.codegencore.model.enums.*;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.EmbeddingResponseModel;
import com.alipay.codegencore.model.model.SegmentInfo;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.tool.learning.FunctionCallConfig;
import com.alipay.codegencore.model.model.yuque.YuQueBookResponseModel;
import com.alipay.codegencore.model.model.yuque.YuQueDocModel;
import com.alipay.codegencore.model.model.yuque.YuQueGroupResponseModel;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.request.ZarkEmbeddingRequestBean;
import com.alipay.codegencore.model.util.DocumentExtInfoKey;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.*;
import com.alipay.codegencore.service.common.segment.SegmentationStrategyFactory;
import com.alipay.codegencore.service.impl.YuQueDocUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.zsearch.ZsearchCommonService;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.dingding.DingDingUtil;
import com.alipay.codegencore.utils.file.FileProcessUtils;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zsearch.common.util.KnnConstant;
import com.alipay.zsearch.core.Search;
import com.alipay.zsearch.core.SearchResult;
import com.alipay.zsearch.core.query.BoolQueryBuilder;
import com.alipay.zsearch.core.query.KnnVectorQueryBuilder;
import com.alipay.zsearch.core.query.QueryBuilder;
import com.alipay.zsearch.core.query.QueryBuilders;
import com.alipay.zsearch.core.search.SearchSourceBuilder;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文档处理实现
 */
@Service
public class DocumentHandleServiceImpl implements DocumentHandleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentHandleService.class);
    @Resource
    private DocumentDOMapper documentDOMapper;
    @Resource
    private OssService ossService;
    @Resource
    private ExecutorService appThreadPool;
    @Resource
    private ChatSessionManageService chatSessionManageService;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private ZarkService zarkService;
    @Resource
    private ZsearchCommonService zsearchCommonService;
    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;
    @Resource
    private AlgoBackendService algoBackendService;
    @Resource
    private SceneService sceneService;
    @Resource
    private UserAclService userAclService;
    @Resource
    private YuQueDocUtilService yuQueDocUtilService;
    @Resource
    private SegmentationStrategyFactory segmentationStrategyFactory;

    @Override
    public void uploadSessionDocument(String sessionUid, List<MultipartFile> fileList, UserAuthDO userAuthDO, String segmentationStrategy) {
        if (StringUtils.isBlank(sessionUid) || CollectionUtils.isEmpty(fileList)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        processDocumentChatConfig(null, sessionUid, documentChatConfig, segmentationStrategy);
        for (MultipartFile file : fileList) {
            try {
                InputStream inputStream = file.getInputStream();
                // 上传源文件到OSS
                String ossSourceFileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
                Pair<String, String> pair = saveDocumentToOss(AppConstants.DOCUMENT_SOURCE_FILE, inputStream,ossSourceFileName);
                String documentUid = pair.getKey();
                DocumentDO documentDO = getDocumentDO(file, pair, userAuthDO.getId(),ossSourceFileName);
                // 源文件存储数据库
                documentDOMapper.insertSelective(documentDO);
                // 文件UID绑定到会话上
                chatSessionManageService.bindDocument(sessionUid, documentUid);
                // 异步解析处理文件
                appThreadPool.execute(() -> parseFile(documentUid, documentChatConfig, userAuthDO.getEmpId()));
            } catch (Exception e) {
                LOGGER.error("uploadSessionFile failed,sessionUid:" + sessionUid, e);
            }
        }
    }

    @Override
    public void uploadSceneDocument(Long sceneId, List<MultipartFile> fileList, UserAuthDO userAuthDO, String segmentationStrategy) {
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        processDocumentChatConfig(sceneId, null, documentChatConfig, segmentationStrategy);
        for (MultipartFile file : fileList) {
            try {
                InputStream inputStream = file.getInputStream();
                // 上传源文件到OSS
                String ossSourceFileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
                Pair<String, String> pair = saveDocumentToOss(AppConstants.DOCUMENT_SOURCE_FILE, inputStream,ossSourceFileName);
                String documentUid = pair.getKey();
                DocumentDO documentDO = getDocumentDO(file, pair, userAuthDO.getId(),ossSourceFileName);
                // 源文件存储数据库
                documentDOMapper.insertSelective(documentDO);
                // 文件UID绑定到助手上
                sceneService.bindDocument(sceneId, documentUid);
                // 异步解析处理文件
                appThreadPool.execute(() -> parseFile(documentUid, documentChatConfig, userAuthDO.getEmpId()));
            } catch (Exception e) {
                LOGGER.error("uploadSceneFile failed,sceneId:" + sceneId, e);
            }
        }
    }

    @Override
    public void uploadSceneYuQueBook(Long sceneId, UserAuthDO userAuthDO, String segmentationStrategy, String yuQueToken, Long bookId, List<Long> docIdList, List<String> docSlugList) {
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        if(docSlugList.size()>documentChatConfig.getInteger("bindMaxSize")){
            throw new BizException(ResponseEnum.UPLOAD_FILE_FAILED,"绑定失败，一次绑定文档数量超过100上限");
        }
        processDocumentChatConfig(sceneId, null, documentChatConfig, segmentationStrategy);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String documentUid = ShortUid.getUid();
        try {
            DocumentDO documentDO = new DocumentDO();
            documentDO.setCreateUserId(userAuthDO.getId());
            documentDO.setUid(documentUid);
            documentDO.setDocumentName(getYuQueBookName(yuQueToken, bookId));
            documentDO.setDocumentStatus(DocumentStatusEnum.INIT.name());
            documentDO.setSource(DocumentSourceEnum.YUQUE_BOOK.name());
            documentDO.setZsearchClient(ZsearchClientEnum.INDEPENDENT.name());
            JSONObject extInfo = new JSONObject();
            extInfo.put(DocumentExtInfoKey.YUQUE_TOKEN, yuQueToken);
            YuQueGroupResponseModel groupInfo = yuQueDocUtilService.getGroupLogin(yuQueToken);
            if(StringUtils.isBlank(groupInfo.getLogin())){
                throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE);
            }
            extInfo.put(DocumentExtInfoKey.TEAM_NAME, groupInfo.getName());
            extInfo.put(DocumentExtInfoKey.TEAM_ID, groupInfo.getId());
            extInfo.put(DocumentExtInfoKey.BOOK_ID, bookId);
            extInfo.put(DocumentExtInfoKey.DOC_ID_LIST, docIdList);
            extInfo.put(DocumentExtInfoKey.DOC_SLUG_LIST,docSlugList);
            extInfo.put(DocumentExtInfoKey.LAST_UPDATE_TIME, sdf.format(new Date()));
            documentDO.setExtInfo(extInfo.toJSONString());
            // 源文件存储数据库
            documentDOMapper.insertSelective(documentDO);
            // 文件UID绑定到助手上
            sceneService.bindDocument(sceneId, documentUid);
            // 异步解析处理文件
            appThreadPool.execute(() -> parseFile(documentUid, documentChatConfig, userAuthDO.getEmpId()));
        } catch (Exception e) {
            LOGGER.error("uploadSceneYuQueBook failed,sceneId:" + sceneId, e);
        }
    }

    private String getYuQueBookName(String yuQueToken, Long bookId) {
        List<YuQueBookResponseModel> groupBooks = yuQueDocUtilService.getGroupBooks(yuQueToken);
        for (YuQueBookResponseModel groupBook : groupBooks) {
            if(groupBook.getId().equals(bookId)){
                return groupBook.getName();
            }
        }
        return null;
    }

    @Override
    public String summeryDocument(String documentUid) {
        DocumentDOExample documentDOExample = new DocumentDOExample();
        documentDOExample.createCriteria().andUidEqualTo(documentUid);
        DocumentDO documentDO = documentDOMapper.selectByExample(documentDOExample).get(0);
        if (DocumentStatusEnum.PARSE_FAILED.name().equals(documentDO.getDocumentStatus())) {
            throw new BizException(ResponseEnum.DOCUMENT_PARSE_FAILED);
        }
        if (!DocumentStatusEnum.READY.name().equals(documentDO.getDocumentStatus())) {
            throw new BizException(ResponseEnum.DOCUMENT_NOT_READY);
        }
        return documentDO.getSummary();
    }

    @Override
    public List<SegmentInfo> recallSegment(String sessionUid, String query) {
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        String embeddingModel = documentChatConfig.getString("embeddingModel");
        Integer topNSimilarity = documentChatConfig.getInteger("topNSimilarity");
        Double minSimilarity = documentChatConfig.getDouble("minSimilarity");
        Integer docListMaxSize = documentChatConfig.getInteger("docListMaxSize");
        ZarkEmbeddingRequestBean zarkEmbeddingRequestBean = new ZarkEmbeddingRequestBean();
        zarkEmbeddingRequestBean.setQueries(Lists.newArrayList(query));
        zarkEmbeddingRequestBean.setModel(embeddingModel);
        List<BigDecimal> queryEmbeddingList = zarkService.embedding(zarkEmbeddingRequestBean).get(0);
        ChatSessionDO sessionDO = chatSessionManageService.getChatSession(sessionUid);
        JSONArray documentUidArr = new JSONArray();
        if (sessionDO.getSceneId() != null) {
            SceneDO sceneDO = sceneService.getSceneById(sessionDO.getSceneId());
            if (StringUtils.isNotEmpty(sceneDO.getDocumentUidList())) {
                documentUidArr.addAll(JSONArray.parseArray(sceneDO.getDocumentUidList()));
            }
        }
        if (StringUtils.isNotEmpty(sessionDO.getDocumentUidList())) {
            documentUidArr.addAll(JSONArray.parseArray(sessionDO.getDocumentUidList()));
        }
        List<SegmentInfo> segmentInfoList = new ArrayList<>();
        if (!documentUidArr.isEmpty()) {
            List<DocumentDO> documentDOList = getDocumentDOList(documentUidArr);
            segmentInfoList = recallFromZSearch(queryEmbeddingList, documentDOList, topNSimilarity, minSimilarity.floatValue());
            segmentInfoList = segmentInfoList.stream().sorted(Comparator.comparing(SegmentInfo::getSimilarity).reversed()).limit(topNSimilarity).collect(Collectors.toList());
        }
        List<SegmentInfo> result = new ArrayList<>();
        int retSize = 0;
        for (SegmentInfo segmentInfo : segmentInfoList) {
            if (segmentInfo.getSegmentContent().length() + retSize <= docListMaxSize) {
                result.add(segmentInfo);
                retSize += segmentInfo.getSegmentContent().length();
            } else {
                break;
            }
        }
        return result;
    }

    @Override
    public List<SegmentInfo> recallSegmentBySceneId(Long sceneId, String query,Integer topNSimilarity, Double minSimilarity) {
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        String embeddingModel = documentChatConfig.getString("embeddingModel");
        if(topNSimilarity==null || topNSimilarity <=0){
            topNSimilarity = documentChatConfig.getInteger("topNSimilarity");
        }
        // 如果接口参数提供了minSimilarity，则使用接口参数，否则使用drm配置中的值
        Double finalMinSimilarity = minSimilarity != null ? minSimilarity : documentChatConfig.getDouble("minSimilarity");
        Integer docListMaxSize = documentChatConfig.getInteger("docListMaxSize");
        ZarkEmbeddingRequestBean zarkEmbeddingRequestBean = new ZarkEmbeddingRequestBean();
        zarkEmbeddingRequestBean.setQueries(Lists.newArrayList(query));
        zarkEmbeddingRequestBean.setModel(embeddingModel);
        List<BigDecimal> queryEmbeddingList = zarkService.embedding(zarkEmbeddingRequestBean).get(0);
        JSONArray documentUidArr = new JSONArray();
        if (sceneId != null) {
            SceneDO sceneDO = sceneService.getSceneById(sceneId);
            if (sceneDO!=null && StringUtils.isNotEmpty(sceneDO.getDocumentUidList())) {
                documentUidArr.addAll(JSONArray.parseArray(sceneDO.getDocumentUidList()));
            }
        }
        List<SegmentInfo> segmentInfoList = new ArrayList<>();
        if (!documentUidArr.isEmpty()) {
            List<DocumentDO> documentDOList = getDocumentDOList(documentUidArr);
            segmentInfoList = recallFromZSearch(queryEmbeddingList, documentDOList, topNSimilarity, finalMinSimilarity.floatValue());
            segmentInfoList = segmentInfoList.stream().sorted(Comparator.comparing(SegmentInfo::getSimilarity).reversed()).limit(topNSimilarity).collect(Collectors.toList());
        }
        List<SegmentInfo> result = new ArrayList<>();
        int retSize = 0;
        for (SegmentInfo segmentInfo : segmentInfoList) {
            if (StringUtils.length(segmentInfo.getSegmentContent()) + retSize <= docListMaxSize) {
                result.add(segmentInfo);
                retSize += segmentInfo.getSegmentContent().length();
            } else {
                break;
            }
        }
        return result;
    }

    @Override
    public String summeryContent(String content, String empId, JSONObject documentChatConfig) {
        String summaryModel = documentChatConfig.getString("summaryModel");
        Integer fileSummarySize = documentChatConfig.getInteger("fileSummarySize");
        String originalStr = content.substring(0, Math.min(fileSummarySize, content.length()));
        AlgoBackendDO summaryModelDO = algoBackendService.getAlgoBackendByName(summaryModel);
        String requestId = ShortUid.getUid();
        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        List<ChatMessage> messages = new ArrayList<>();
        String promptFormat = documentChatConfig.getString("promptFormat");
        String summaryFileQuestionPrompt = documentChatConfig.getString("summaryFileQuestionPrompt");
        promptFormat = promptFormat.replace("question", summaryFileQuestionPrompt);
        StringBuilder searchResult = new StringBuilder("Source 〔1〕");
        searchResult.append("\n").append(originalStr);
        promptFormat = promptFormat.replace("searchResult", searchResult);
        messages.add(new ChatMessage("user", promptFormat));
        chatCompletionRequest.setMessages(messages);
        // 请求模型的时候加上工号，兼容AntGLM模型
        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
        chatRequestExtData.setEmpId(empId);
        chatCompletionRequest.setChatRequestExtData(chatRequestExtData);
        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, AppConstants.CODEGPT_TOKEN_USER, false, summaryModelDO, chatCompletionRequest, null);
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey) -> noneSerializationCacheManager.del(tBaseKey);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setUniqueAnswerId(requestId);
        ChatMessage chatMessage = AlgoModelExecutor.getInstance().executorChat(summaryModelDO, params);
        return chatMessage.getContent();
    }

    private List<DocumentDO> getDocumentDOList(JSONArray documentUidList) {
        DocumentDOExample documentDOExample = new DocumentDOExample();
        documentDOExample.createCriteria().andUidIn(documentUidList.toJavaList(String.class));
        return documentDOMapper.selectByExample(documentDOExample);
    }

    /**
     * @param inputStream
     * @return <documentUid,ossUrl>
     */
    private Pair<String,String> saveDocumentToOss(String path, InputStream inputStream, String  ossSourceFileName) {
        String uid = ShortUid.getUid();
        String filePath;
        if (StrUtil.isNotBlank(ossSourceFileName)) {
            filePath = path + ossSourceFileName;
        } else {
            filePath = path + uid;
        }
        String fileOssUrl = ossService.putObject(filePath, inputStream, null, AppConstants.CONTENT_TYPE_DOWNLOAD);
        return new Pair<>(uid, fileOssUrl);
    }

    /**
     * 保存embedding结果到ZSearch
     * @param segmentList
     * @throws IOException
     */
    private void saveSegmentToZSearch(List<EmbeddingResponseModel> segmentList) throws IOException {
        zsearchCommonService.saveDataList(segmentList, ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, EmbeddingResponseModel::getPartUid,ZsearchClientEnum.INDEPENDENT);
    }

    /**
     * 保存embedding结果到ZSearch
     * @param queryEmbeddingList
     * @param documentDOList 文档信息列表，不可用为空
     */
    private List<SegmentInfo> recallFromZSearch(List<BigDecimal> queryEmbeddingList, List<DocumentDO> documentDOList, int topNSimilarity, float minSimilarity){
        List<DocumentDO> sharedDocumentDOList = new ArrayList<>();
        List<DocumentDO> independentDocumentDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(documentDOList)) {
            sharedDocumentDOList = documentDOList.stream().filter(documentDO -> StringUtils.equalsIgnoreCase(documentDO.getZsearchClient(), ZsearchClientEnum.SHARED.name())||StringUtils.isBlank(documentDO.getZsearchClient())).collect(Collectors.toList());
            independentDocumentDOList = documentDOList.stream().filter(documentDO -> StringUtils.equalsIgnoreCase(documentDO.getZsearchClient(), ZsearchClientEnum.INDEPENDENT.name())).collect(Collectors.toList());
        }
        Map<String, DocumentDO> documentDOMap = documentDOList.stream().collect(Collectors.toMap(DocumentDO::getUid, Function.identity()));
        List<SegmentInfo> segmentInfoList = new ArrayList<>();
        try {
            List<SearchResult.Hit<EmbeddingResponseModel, Void>> hits = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(sharedDocumentDOList)){
                SearchSourceBuilder searchSourceBuilder = makeSearchSourceBuilder(sharedDocumentDOList, topNSimilarity, queryEmbeddingList, minSimilarity);
                Search search = new Search.Builder().addIndex(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT.getIndexName()).addType(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT.getTypeName()).source(searchSourceBuilder).build();
                SearchResult searchResult = zsearchCommonService.getZSearchRestClient(ZsearchClientEnum.SHARED).execute(search);
                if (!searchResult.isSucceeded()) {
                    LOGGER.error("查询shared client zsearch数据异常.{}", searchResult.getErrorMessage());
                    throw new BizException(ResponseEnum.ZSEARCH_QUERY_FAILED);
                }
                hits.addAll(searchResult.getHits(EmbeddingResponseModel.class));
            }
            if(CollectionUtils.isNotEmpty(independentDocumentDOList)){
                SearchSourceBuilder searchSourceBuilder = makeSearchSourceBuilder(independentDocumentDOList, topNSimilarity, queryEmbeddingList, minSimilarity);
                Search search = new Search.Builder().addIndex(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT.getIndexName()).addType(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT.getTypeName()).source(searchSourceBuilder).build();
                SearchResult searchResult = zsearchCommonService.getZSearchRestClient(ZsearchClientEnum.INDEPENDENT).execute(search);
                if(!searchResult.isSucceeded()){
                    LOGGER.error("查询independent client zsearch数据异常.{}",searchResult.getErrorMessage());
                    throw new BizException(ResponseEnum.ZSEARCH_QUERY_FAILED);
                }
                hits.addAll(searchResult.getHits(EmbeddingResponseModel.class));
            }
            if(CollectionUtils.isNotEmpty(hits)){
                for (SearchResult.Hit<EmbeddingResponseModel, Void> hit: hits){
                    EmbeddingResponseModel source = hit.source;
                    double score = hit.score;
                    SegmentInfo segmentInfo = new SegmentInfo(source);
                    segmentInfo.setSimilarity(new BigDecimal(score));
                    segmentInfoList.add(segmentInfo);
                }
            }
        } catch (Exception e) {
            LOGGER.error("查询zsearch数据异常.", e);
            throw new BizException(ResponseEnum.ZSEARCH_QUERY_FAILED);
        }

        for (SegmentInfo segmentInfo : segmentInfoList) {
            String docUid = segmentInfo.getDocumentUid();
            DocumentDO documentDO = documentDOMap.get(docUid);

            String quoteDocumentUrl;
            JSONObject extInfo = JSON.parseObject(documentDO.getExtInfo());
            if (DocumentSourceEnum.UPLOAD.name().equals(documentDO.getSource())) {
                quoteDocumentUrl = extInfo.getString(DocumentExtInfoKey.FILE_OSS_URL);
                segmentInfo.setQuoteDocumentUrl(quoteDocumentUrl);
            }

            segmentInfo.setDocumentName(documentDO.getDocumentName());
        }
        return segmentInfoList;
    }

    @Override
    public void parseFile(String documentUid, JSONObject documentChatConfig, String empId) {
        try {
            DocumentDO documentDO = getDocumentDOList(new JSONArray(Lists.newArrayList(documentUid))).get(0);
            JSONObject extInfo = JSONObject.parseObject(documentDO.getExtInfo());
            String embeddingModel = documentChatConfig.getString("embeddingModel");
            String content;
            List<EmbeddingResponseModel> embeddingResponseModelList = new ArrayList<>();
            if (DocumentSourceEnum.UPLOAD.name().equals(documentDO.getSource())) {
                String fileExtension = extInfo.getString(DocumentExtInfoKey.FILE_TYPE);
                InputStream inputStream = ossService.getInputStream(AppConstants.DOCUMENT_SOURCE_FILE + extInfo.getString(DocumentExtInfoKey.OSS_SOURCE_FILE_NAME));
                content = FileProcessUtils.getFileContent(fileExtension, inputStream);
                List<String> segmentContent = segmentationStrategyFactory.getSegmentationStrategy(SegmentationStrategyTypeEnum.valueOf(documentChatConfig.getString("segmentationStrategy"))).segment(content, documentChatConfig);
                embeddingResponseModelList = zarkService.embeddingStrList(segmentContent, embeddingModel);
                embeddingResponseModelList.forEach(e -> {
                    e.setDocTitle(documentDO.getDocumentName());
                    e.setDcoUrl(extInfo.getString(DocumentExtInfoKey.FILE_OSS_URL));
                });
            } else if (DocumentSourceEnum.YUQUE_BOOK.name().equals(documentDO.getSource())) {
                content = getYuQueContent(documentChatConfig, documentDO, extInfo, embeddingModel, embeddingResponseModelList);
            } else {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "文档来源未知");
            }
            Pair<String, String> contentPair = saveDocumentToOss(AppConstants.DOCUMENT_CONTENT, IOUtils.toInputStream(content, "UTF-8"),null);
            embeddingResponseModelList.forEach(e -> e.setFileUid(documentUid));
            saveSegmentToZSearch(embeddingResponseModelList);
            UserAuthDO userAuthDO = userAclService.queryUserByEmpId(empId);
            saveDocument(documentUid, documentChatConfig, userAuthDO, documentDO, extInfo, content, contentPair);
        } catch (Throwable e) {
            LOGGER.error("解析总结文件失败,documentUid:" + documentUid, e);
            DocumentDOExample documentDOExample = new DocumentDOExample();
            documentDOExample.createCriteria().andUidEqualTo(documentUid);
            DocumentDO documentDO = new DocumentDO();
            documentDO.setDocumentStatus(DocumentStatusEnum.PARSE_FAILED.name());
            documentDOMapper.updateByExampleSelective(documentDO, documentDOExample);
        }
    }
    @Override
    public void deleteSceneDocument(String documentUid, Long sceneId) {
        if(!checkDeleteDocument(documentUid)){
            throw new BizException(ResponseEnum.DOCUMENT_NOT_READY,"操作失败，原文档还未生效，请稍后再试");
        }
        DocumentDOExample documentDOExample = new DocumentDOExample();
        documentDOExample.createCriteria().andUidEqualTo(documentUid);
        List<DocumentDO> documentDOS = documentDOMapper.selectByExample(documentDOExample);
        if (CollectionUtils.isEmpty(documentDOS)) {
            return;
        }
        DocumentDO documentDO = documentDOS.get(0);
        String uid = JSONObject.parseObject(documentDO.getExtInfo()).getString(DocumentExtInfoKey.DOCUMENT_CONTENT_OSS_UID);
        // 删除OSS的文件文本
        ossService.deleteFile(AppConstants.DOCUMENT_CONTENT + uid);
        if (DocumentSourceEnum.UPLOAD.name().equals(documentDO.getSource())) {
            // 删除用户上传的源文件
            JSONObject extInfo = JSONObject.parseObject(documentDO.getExtInfo());
            ossService.deleteFile(AppConstants.DOCUMENT_SOURCE_FILE + extInfo.getString(DocumentExtInfoKey.OSS_SOURCE_FILE_NAME));
        }
        // 删除zs的分段数据
        try {
            QueryBuilder queryBuilder = QueryBuilders.termQuery("fileUid", documentUid);
            if(StringUtils.isBlank(documentDO.getZsearchClient())||ZsearchClientEnum.SHARED.name().equals(documentDO.getZsearchClient())){
                zsearchCommonService.deleteData(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, queryBuilder,ZsearchClientEnum.SHARED);
            }else {
                zsearchCommonService.deleteData(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, queryBuilder,ZsearchClientEnum.INDEPENDENT);
            }

        } catch (Exception e) {
            LOGGER.warn("删除zSearch数据异常,documentUid:" + documentUid, e);
        }
        // 删除数据库的文档数据
        documentDOMapper.deleteByExample(documentDOExample);
        sceneService.unbindDocument(sceneId, documentUid);
    }

    @Override
    public List<Long> getDocIdsFromDocument(Long documentId) {
        DocumentDO documentDO = documentDOMapper.selectByPrimaryKey(documentId);
        if(documentDO!=null){
            JSONArray docIdArray = JSONObject.parseObject(documentDO.getExtInfo()).getJSONArray(DocumentExtInfoKey.DOC_ID_LIST);
            if(docIdArray!=null){
                return docIdArray.toJavaList(Long.class);
            }
        }
        return new ArrayList<>();
    }
    @Override
    public List<String> getDocSlugListFromDocument(Long documentId) {
        DocumentDO documentDO = documentDOMapper.selectByPrimaryKey(documentId);
        if(documentDO!=null){
            JSONArray docIdArray = JSONObject.parseObject(documentDO.getExtInfo()).getJSONArray(DocumentExtInfoKey.DOC_SLUG_LIST);
            if(docIdArray!=null){
                return docIdArray.toJavaList(String.class);
            }
        }
        return new ArrayList<>();
    }
    @Override
    public Boolean checkDeleteDocument(String documentUid) {
        if(StringUtils.isBlank(documentUid)){
            return false;
        }
        DocumentDO documentDO = getDocumentByUid(documentUid);
        if(documentDO == null){
            return false;
        }
        if(documentDO.getDocumentStatus().equalsIgnoreCase(DocumentStatusEnum.INIT.name())){
            return false;
        }
        return true;
    }

    @Override
    public DocumentDO getDocumentByUid(String documentUid) {
        if(StringUtils.isBlank(documentUid)){
            return null;
        }
        DocumentDOExample documentDOExample = new DocumentDOExample();
        documentDOExample.createCriteria().andUidEqualTo(documentUid);
        List<DocumentDO> documentDOS = documentDOMapper.selectByExample(documentDOExample);
        if(CollectionUtils.isNotEmpty(documentDOS)){
            return documentDOS.get(0);
        }
        return null;
    }

    @Override
    public List<DocumentDO> getDocumentByUidList(List<String> documentUidList) {
        if (CollectionUtils.isEmpty(documentUidList)) {
            return null;
        }
        DocumentDOExample documentDOExample = new DocumentDOExample();
        documentDOExample.createCriteria().andUidIn(documentUidList);
        documentDOExample.setOrderByClause("id DESC");

        return documentDOMapper.selectByExample(documentDOExample);
    }

    @Override
    public List<DocumentDO> getDocumentBySceneId(Long sceneId) {
        if(sceneId == null){
            return new ArrayList<>();
        }
        SceneDO scene = sceneService.getSceneById(sceneId);
        if(StringUtils.isNotBlank(scene.getDocumentUidList())){
            List<String> documentUidList = JSONArray.parseArray(scene.getDocumentUidList(), String.class);
            if(CollectionUtils.isEmpty(documentUidList)){
                return new ArrayList<>();
            }
            DocumentDOExample documentDOExample = new DocumentDOExample();
            documentDOExample.createCriteria().andUidIn(documentUidList);
            documentDOExample.setOrderByClause("id DESC");
            List<DocumentDO> documentDOS = documentDOMapper.selectByExample(documentDOExample);
            return documentDOS;
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean checkYuQueBookExist(Long sceneId, Long bookId) {
        List<DocumentDO> documentDOs = getDocumentBySceneId(sceneId);
        if(CollectionUtils.isEmpty(documentDOs)){
            return false;
        }
        for (DocumentDO documentDO : documentDOs) {
            if(documentDO.getSource().equalsIgnoreCase(DocumentSourceEnum.YUQUE_BOOK.name())){
                if(JSONObject.parseObject(documentDO.getExtInfo()).getLong(DocumentExtInfoKey.BOOK_ID).equals(bookId)){
                    return true;
                }
            }
        }
        return false;
    }

    @NotNull
    private static DocumentDO getDocumentDO(MultipartFile file, Pair<String, String> pair, Long userId,String ossSourceFileName) {
        DocumentDO documentDO = new DocumentDO();
        documentDO.setCreateUserId(userId);
        documentDO.setUid(pair.getKey());
        documentDO.setDocumentName(file.getOriginalFilename());
        documentDO.setDocumentSize(file.getSize());
        documentDO.setDocumentStatus(DocumentStatusEnum.INIT.name());
        documentDO.setSource(DocumentSourceEnum.UPLOAD.name());
        documentDO.setZsearchClient(ZsearchClientEnum.INDEPENDENT.name());
        JSONObject extInfo = new JSONObject();
        extInfo.put(DocumentExtInfoKey.FILE_OSS_URL, pair.getValue());
        String fileExtension = org.springframework.util.StringUtils.getFilenameExtension(file.getOriginalFilename());
        extInfo.put(DocumentExtInfoKey.FILE_TYPE, fileExtension);
        extInfo.put(DocumentExtInfoKey.OSS_SOURCE_FILE_NAME, ossSourceFileName);
        documentDO.setExtInfo(extInfo.toJSONString());
        return documentDO;
    }

    /**
     * 定时任务更新语雀文档
     *
     * @param documentDO
     * @throws IOException
     */
    @Override
    public void updateYuQueDocument(DocumentDO documentDO,JSONObject documentChatConfig) throws IOException {

        JSONObject extInfo = JSONObject.parseObject(documentDO.getExtInfo());
        String embeddingModel = documentChatConfig.getString("embeddingModel");
        List<EmbeddingResponseModel> embeddingResponseModelList = new ArrayList<>();
        // 查询出旧的document_content_oss_uid，partUidList
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("fileUid", documentDO.getUid()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        List<EmbeddingResponseModel> embeddingResponseModels;
        if(StringUtils.isBlank(documentDO.getZsearchClient())||ZsearchClientEnum.SHARED.name().equals(documentDO.getZsearchClient())){
            embeddingResponseModels = zsearchCommonService.queryData(
                    ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, searchSourceBuilder, 10, EmbeddingResponseModel.class,ZsearchClientEnum.SHARED);
        }else {
            embeddingResponseModels = zsearchCommonService.queryData(
                    ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, searchSourceBuilder, 10, EmbeddingResponseModel.class,ZsearchClientEnum.INDEPENDENT);
        }

        List<String> partUidList = embeddingResponseModels.stream().map(EmbeddingResponseModel::getPartUid).collect(
                Collectors.toList());
        // 获取解析后的语雀文本
        processDocumentChatConfig(null,null,documentChatConfig,null);
        String yuQueContent = getYuQueContent(documentChatConfig, documentDO, extInfo, embeddingModel, embeddingResponseModelList);
        // 新的 文本写入 oss，zs
        Pair<String,String> contentPair = saveDocumentToOss(AppConstants.DOCUMENT_CONTENT, IOUtils.toInputStream(yuQueContent, "UTF-8"),null);
        embeddingResponseModelList.forEach(e -> e.setFileUid(documentDO.getUid()));
        saveSegmentToZSearch(embeddingResponseModelList);

        // 删除旧OSS的文件文本
        String uid = JSONObject.parseObject(documentDO.getExtInfo()).getString(DocumentExtInfoKey.DOCUMENT_CONTENT_OSS_UID);
        ossService.deleteFile(AppConstants.DOCUMENT_CONTENT + uid);
        // 删除旧zs的分段数据
        try {
            QueryBuilder queryBuilder = QueryBuilders.termsQuery("partUid", partUidList);
            if(StringUtils.isBlank(documentDO.getZsearchClient())||ZsearchClientEnum.SHARED.name().equals(documentDO.getZsearchClient())){
                zsearchCommonService.deleteData(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, queryBuilder,ZsearchClientEnum.SHARED);
            }else {
                zsearchCommonService.deleteData(ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, queryBuilder,ZsearchClientEnum.INDEPENDENT);
            }

        } catch (Exception e) {
            LOGGER.warn(String.format("删除zSearch数据异常,document:%s,partUidList:%s",JSONObject.toJSONString(documentDO),JSONObject.toJSONString(partUidList)), e);
            DingDingUtil.sendMessage(String.format("删除zSearch数据异常,document:%s,partUidList:%s",JSONObject.toJSONString(documentDO),JSONObject.toJSONString(partUidList)));
            throw new RuntimeException(e);
        }
        // 更新数据库的documentDO
        UserAuthDO userAuthDO = userAclService.selectByUserId(documentDO.getCreateUserId());
        saveDocument(documentDO.getUid(), documentChatConfig, userAuthDO, documentDO, extInfo, yuQueContent, contentPair);
    }

    /**
     * 把助手或会话里的一些对文档的配置传入documentChatConfig
     * 分段策略优先级：接口维度策略 > scene维度配置 > 默认策略
     * @param sceneId
     * @param sessionId
     * @param documentChatConfig
     */
    private void processDocumentChatConfig(Long sceneId, String sessionId, JSONObject documentChatConfig, String segmentationStrategy) {
        // 上传文件接口维度选择的分段策略
        if (StringUtils.isNotBlank(segmentationStrategy)) {
            documentChatConfig.put("segmentationStrategy", segmentationStrategy);
            return;
        }

        // scene维度配置的分段策略
        if (sceneId != null || sessionId != null) {
            SceneDO sceneDO = null;
            if (sessionId != null) {
                ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionId);
                sceneDO = sceneService.getSceneById(chatSessionDO.getSceneId());
            }
            if (sceneId != null) {
                sceneDO = sceneService.getSceneById(sceneId);
            }
            FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(sceneDO);
            if (functionCallConfig.getSegmentationStrategyType() != null) {
                documentChatConfig.put("segmentationStrategy", functionCallConfig.getSegmentationStrategyType());
            }
        }

        // 默认的文本分段策略
        if (!documentChatConfig.containsKey("segmentationStrategy") || StringUtils.isBlank(documentChatConfig.getString("segmentationStrategy"))) {
            documentChatConfig.put("segmentationStrategy", "DELIMITER_STRATEGY");
        }
    }
    /**
     * 获取解析后的语雀文档文本
     *
     * @param documentChatConfig
     * @param documentDO
     * @param extInfo
     * @param embeddingModel
     * @param embeddingResponseModelList
     * @return
     */
    public String getYuQueContent(JSONObject documentChatConfig, DocumentDO documentDO, JSONObject extInfo, String embeddingModel,
                             List<EmbeddingResponseModel> embeddingResponseModelList) {
        String content;
        List<Long> docIdList;
        List<String> docSlugList;
        String yuQueToken = extInfo.getString(DocumentExtInfoKey.YUQUE_TOKEN);
        Long bookId = extInfo.getLong(DocumentExtInfoKey.BOOK_ID);
        JSONArray docIdArray = extInfo.getJSONArray(DocumentExtInfoKey.DOC_ID_LIST);
        JSONArray docSlugArray = extInfo.getJSONArray(DocumentExtInfoKey.DOC_SLUG_LIST);
        // 避免npe
        if(docIdArray != null){
            docIdList = docIdArray.toJavaList(Long.class);
        }else {
            docIdList = new ArrayList<>();
        }
        if(docSlugArray != null){
            docSlugList = docSlugArray.toJavaList(String.class);
        }else {
            docSlugList = new ArrayList<>();
        }
        List<YuQueDocModel> yuQueDocModels = yuQueDocUtilService.yuQueDocExport(yuQueToken, bookId, docIdList, docSlugList);
        StringBuilder sb = new StringBuilder();
        for (YuQueDocModel yuQueDocModel : yuQueDocModels) {
            sb.append(yuQueDocModel.getContent()).append("\n");
            List<String> segmentContent = segmentationStrategyFactory.getSegmentationStrategy(SegmentationStrategyTypeEnum.valueOf(documentChatConfig.getString("segmentationStrategy")))
                    .segment(yuQueDocModel.getContent(), documentChatConfig);
            List<EmbeddingResponseModel> responseModelList = zarkService.embeddingStrList(segmentContent, embeddingModel);
            responseModelList.forEach(e->e.setDcoUrl(yuQueDocModel.getDocUrl()));
            responseModelList.forEach(e->e.setDocTitle(yuQueDocModel.getTitle()));
            embeddingResponseModelList.addAll(responseModelList);
        }
        content = sb.toString();
        documentDO.setDocumentSize((long) content.getBytes(StandardCharsets.UTF_8).length);
        return content;
    }

    /**
     * 文本解析成功后相关参数存入数据库
     *
     * @param documentUid
     * @param documentChatConfig
     * @param documentDO
     * @param extInfo
     * @param content
     * @param contentPair
     */
    private void saveDocument(String documentUid, JSONObject documentChatConfig, UserAuthDO userAuthDO, DocumentDO documentDO,
                           JSONObject extInfo, String content, Pair<String,String> contentPair) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        documentDO.setContentOssUrl(contentPair.getValue());
        documentDO.setContentLength((long) content.length());
        documentDO.setSummary(summeryContent(content, userAuthDO.getEmpId(), documentChatConfig));
        extInfo.put(DocumentExtInfoKey.DOCUMENT_CONTENT_OSS_UID, contentPair.getKey());
        extInfo.put(DocumentExtInfoKey.LAST_UPDATE_TIME, sdf.format(new Date()));
        documentDO.setExtInfo(extInfo.toJSONString());
        documentDO.setDocumentStatus(DocumentStatusEnum.READY.name());
        documentDO.setUpdateUserId(userAuthDO.getId());
        documentDOMapper.updateByPrimaryKeySelective(documentDO);
        LOGGER.info("解析总结文件成功,documentUid:{}", documentUid);
    }

    /**
     * 生成搜索源
     * @param documentDOList
     * @param topNSimilarity
     * @param queryEmbeddingList
     * @param minSimilarity
     * @return
     */
    private SearchSourceBuilder makeSearchSourceBuilder(List<DocumentDO> documentDOList, int topNSimilarity, List<BigDecimal> queryEmbeddingList, float minSimilarity){
        Map<String, DocumentDO> documentDOMap = documentDOList.stream().collect(Collectors.toMap(DocumentDO::getUid, Function.identity()));

        SearchSourceBuilder searchSourceBuilder=new SearchSourceBuilder();
        KnnVectorQueryBuilder knnVectorQueryBuilder = new KnnVectorQueryBuilder(KnnConstant.KNN_ALGORITHM_HNSW, "originalEmbeddingList");
        knnVectorQueryBuilder.size(topNSimilarity);

        knnVectorQueryBuilder.vector(queryEmbeddingList.stream().map(BigDecimal::floatValue).toArray(Float[]::new));
        knnVectorQueryBuilder.minScore(minSimilarity);
        knnVectorQueryBuilder.ef(100);

        knnVectorQueryBuilder.filter(QueryBuilders.termsQuery("fileUid", documentDOMap.keySet()));

        searchSourceBuilder.query(knnVectorQueryBuilder);
        searchSourceBuilder.fetchSource(new String[]{"fileUid","partUid","originalStr","dcoUrl"},null);
        return searchSourceBuilder;
    }

}
