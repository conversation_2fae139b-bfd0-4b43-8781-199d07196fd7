package com.alipay.codegencore.web.openapi;

import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserTypeEnum;
import com.alipay.codegencore.model.model.SegmentInfo;
import com.alipay.codegencore.model.openai.UserSaveSceneVO;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.utils.thread.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.11.20
 */
@RestController
@RequestMapping("/api/plugin")
@Slf4j
public class IdePluginApiController {

    @Resource
    private UserAclService userAclService;

    @Resource
    private SceneService sceneService;

    @Resource
    private UserAuthDOMapper userAuthDOMapper;

    @Resource
    private DocumentHandleService documentHandleService;

    @GetMapping("/docSearch")
    public BaseResponse<List<SegmentInfo>> ragSearch(HttpServletRequest httpServletRequest,
                                                     @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                     @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                                     @RequestHeader(value = "emp_id", required = false) String empId,
                                                     @RequestParam(name = "query") String query,
                                                     @RequestParam(name = "sceneId") Long sceneId,
                                                     @RequestParam(name = "limit", required = false)Integer limit,
                                                     @RequestParam(name = "minSimilarity", required = false) Double minSimilarity){

        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        if(StringUtils.isNotBlank(empId)){
            List<UserAuthDO> userAuthDOList = getUserAuthListByEmpId(empId);
            if(!userAuthDOList.isEmpty()){
                ContextUtil.set(CONTEXT_USER, userAuthDOList.get(0));
                // 鉴权判断当前用户是否有当前scene的权限
                if(userAuthDOList.get(0).getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
                    SceneDO scene = sceneService.getSceneById(sceneId);
                    if(scene.getOwnerUserId().compareTo(userAuthDOList.get(0).getId())!=0){
                        return BaseResponse.build(ResponseEnum.NO_AUTH);
                    }
                }

            }
        }
        List<SegmentInfo> segmentInfos = documentHandleService.recallSegmentBySceneId(sceneId, query, limit, minSimilarity);
        return BaseResponse.build(segmentInfos);
    }

    private List<UserAuthDO> getUserAuthListByEmpId(String empId) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
        return userAuthDOMapper.selectByExample(userAuthDOExample);
    }
    @GetMapping("/pluginEnableScene")
    public BaseResponse<List<UserSaveSceneVO>> getPluginEnableScene(HttpServletRequest httpServletRequest,
                                                              @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                              @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                                              @RequestHeader(value = "emp_id", required = false) String empId){
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        if(StringUtils.isNotBlank(empId)){
            List<UserAuthDO> userAuthDOList = getUserAuthListByEmpId(empId);
            if(!userAuthDOList.isEmpty()){
                ContextUtil.set(CONTEXT_USER, userAuthDOList.get(0));
            }
        }
        return BaseResponse.build(sceneService.getAllSceneByPluginEnable());
    }
}
