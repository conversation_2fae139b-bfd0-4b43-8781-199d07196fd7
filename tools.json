[{"description": "Tool for editing files.\n* `path` is a file path relative to the workspace root\n* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.\n* Generate `instruction_reminder` first to remind yourself to limit the edits to at most 150 lines.\n\nNotes for using the `str_replace` command:\n* Specify `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2` and `old_str_end_line_number_2` for the second replacement, and so on\n* The `old_str_start_line_number_1` and `old_str_end_line_number_1` parameters are 1-based line numbers\n* Both `old_str_start_line_number_1` and `old_str_end_line_number_1` are INCLUSIVE\n* The `old_str_1` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!\n* Empty `old_str_1` is allowed only when the file is empty or contains only whitespaces\n* It is important to specify `old_str_start_line_number_1` and `old_str_end_line_number_1` to disambiguate between multiple occurrences of `old_str_1` in the file\n* Make sure that `old_str_start_line_number_1` and `old_str_end_line_number_1` do not overlap with other `old_str_start_line_number_2` and `old_str_end_line_number_2` entries\n* The `new_str_1` parameter should contain the edited lines that should replace the `old_str_1`. Can be an empty string to delete content\n* To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2` for the second replacement, etc.\n\nNotes for using the `insert` command:\n* Specify `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, and so on\n* The `insert_line_1` parameter specifies the line number after which to insert the new string\n* The `insert_line_1` parameter is 1-based line number\n* To insert at the very beginning of the file, use `insert_line_1: 0`\n* To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, etc.\n\nIMPORTANT:\n* This is the only tool you should use for editing files.\n* If it fails try your best to fix inputs and retry.\n* DO NOT fall back to removing the whole file and recreating it from scratch.\n* DO NOT use sed or any other command line tools for editing files.\n* Try to fit as many edits in one tool call as possible\n* Use the view tool to read files before editing them.\n", "name": "str-replace-editor", "parameters": {"properties": {"command": {"description": "The commands to run. Allowed options are: 'str_replace', 'insert'.", "enum": ["str_replace", "insert"], "type": "string"}, "insert_line_1": {"description": "Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.", "type": "integer"}, "instruction_reminder": {"description": "Reminder to limit edits to at most 150 lines. Should be exactly this string: 'ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.'", "type": "string"}, "new_str_1": {"description": "Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert.", "type": "string"}, "old_str_1": {"description": "Required parameter of `str_replace` command containing the string in `path` to replace.", "type": "string"}, "old_str_end_line_number_1": {"description": "The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.", "type": "integer"}, "old_str_start_line_number_1": {"description": "The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.", "type": "integer"}, "path": {"description": "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.", "type": "string"}}, "required": ["command", "path", "instruction_reminder"], "type": "object"}}, {"description": "Open a URL in the default browser.\n\n1. The tool takes in a URL and opens it in the default browser.\n2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.\n3. You should not use `open-browser` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Each time you call `open-browser`, it will jump the user to the browser window, which is highly annoying to the user.", "name": "open-browser", "parameters": {"properties": {"url": {"description": "The URL to open in the browser.", "type": "string"}}, "required": ["url"], "type": "object"}}, {"description": "Get issues (errors, warnings, etc.) from the IDE. You must provide the paths of the files for which you want to get issues.", "name": "diagnostics", "parameters": {"properties": {"paths": {"description": "Required list of file paths to get issues for from the IDE.", "items": {"type": "string"}, "type": "array"}}, "required": ["paths"], "type": "object"}}, {"description": "Read output from the active or most-recently used VSCode terminal.\n\nBy default, it reads all of the text visible in the terminal, not just the output of the most recent command.\n\nIf you want to read only the selected text in the terminal, set `only_selected=true` in the tool input.\nOnly do this if you know the user has selected text that you want to read.\n\nNote that this is unrelated to the list-processes and read-process tools, which interact with processes that were launched with the \"launch-process\" tool.", "name": "read-terminal", "parameters": {"properties": {"only_selected": {"description": "Whether to read only the selected text in the terminal.", "type": "boolean"}}, "required": [], "type": "object"}}, {"description": "Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`).\n\nIf `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to\n`max_wait_seconds` seconds. If the process ends during this period, the tool call returns. If the timeout\nexpires, the process will continue running in the background but the tool call will return. You can then\ninteract with the process using the other process tools.\n\nNote: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`\nwhile another is running, the tool will return an error.\n\nIf `wait=false`, launches a background process in a separate terminal. This returns immediately, while the\nprocess keeps running in the background.\n\nNotes:\n- Use `wait=true` processes when the command is expected to be short, or when you can't\nproceed with your task until the process is complete. Use `wait=false` for processes that are\nexpected to run in the background, such as starting a server you'll need to interact with, or a\nlong-running process that does not need to complete before proceeding with the task.\n- If this tool returns while the process is still running, you can continue to interact with the process\nusing the other available tools. You can wait for the process, read from it, write to it, kill it, etc.\n- You can use this tool to interact with the user's local version control system. Do not use the\nretrieval tool for that purpose.\n- If there is a more specific tool available that can perform the function, use that tool instead of\nthis one.\n\nThe OS is darwin. The shell is 'zsh'.", "name": "launch-process", "parameters": {"properties": {"command": {"description": "The shell command to execute.", "type": "string"}, "cwd": {"description": "Required parameter. Absolute path to the working directory for the command.", "type": "string"}, "max_wait_seconds": {"description": "Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.", "type": "number"}, "wait": {"description": "Whether to wait for the command to complete.", "type": "boolean"}}, "required": ["command", "wait", "max_wait_seconds", "cwd"], "type": "object"}}, {"description": "Kill a process by its terminal ID.", "name": "kill-process", "parameters": {"properties": {"terminal_id": {"description": "Terminal ID to kill.", "type": "integer"}}, "required": ["terminal_id"], "type": "object"}}, {"description": "Read output from a terminal.\n\nIf `wait=true` and the process has not yet completed, waits for the terminal to complete up to `max_wait_seconds` seconds before returning its output.\n\nIf `wait=false` or the process has already completed, returns immediately with the current output.", "name": "read-process", "parameters": {"properties": {"max_wait_seconds": {"description": "Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed.", "type": "number"}, "terminal_id": {"description": "Terminal ID to read from.", "type": "integer"}, "wait": {"description": "Whether to wait for the command to complete.", "type": "boolean"}}, "required": ["terminal_id", "wait", "max_wait_seconds"], "type": "object"}}, {"description": "Write input to a terminal.", "name": "write-process", "parameters": {"properties": {"input_text": {"description": "Text to write to the process's stdin.", "type": "string"}, "terminal_id": {"description": "Terminal ID to write to.", "type": "integer"}}, "required": ["terminal_id", "input_text"], "type": "object"}}, {"description": "List all known terminals created with the launch-process tool and their states.", "name": "list-processes", "parameters": {"properties": {}, "required": [], "type": "object"}}, {"description": "Search the web for information. Returns results in markdown format.\nEach result includes the URL, title, and a snippet from the page if available.\n\nThis tool uses Google's Custom Search API to find relevant web pages.", "name": "web-search", "parameters": {"description": "Input schema for the web search tool.", "properties": {"num_results": {"default": 5, "description": "Number of results to return", "maximum": 10, "minimum": 1, "title": "Num Results", "type": "integer"}, "query": {"description": "The search query to send.", "title": "Query", "type": "string"}}, "required": ["query"], "title": "WebSearchInput", "type": "object"}}, {"description": "Make GitHub API calls. Response is formatted as yaml.\nAll issues endpoints will return both issues and PRs.\nMost of your work are only considered done when you commit all necessary changes, create a PR, successfully pass all tests, get approved, and merge. Seek permission before you push to remote or perform rebase, unless you are explicitly told to do so.\n\n\nGitHub Information of the current user:\n- Login: sxthunder\n- Name: KyrieMing\n- Email: <EMAIL>\nUse user login instead of email in API parameters.\n\n\nLocal Git Repository Information:\n- Repository Root: /Users/<USER>/projects/codegencore\n- Remote URL: *******************:common_release/codegencore.git\n- Current Branch: tt_4416cdf0adf677237867cae03b1bd4df1b115778\n- Default Branch: master\n- Git User Email: <EMAIL>\nREPOSITORY SCOPE:\nAll queries MUST be limited to this repository only, unless explicitly requested otherwise. Always indicate in text outside of the tool use that you are limiting to this repo if you are doing so.\n", "name": "github-api", "parameters": {"description": "Input schema for the GitHub API tool.", "properties": {"data": {"anyOf": [{"type": "object"}, {"type": "null"}], "default": null, "description": "Data to send - automatically handled as query params for GET or JSON body for POST.Query parameters should be used eagerly. Use small page sizes.", "title": "Data"}, "details": {"default": false, "description": "If false (default), it will only contain the most essential fields which is usually sufficient. If true, the response will include all non-trivial fields. Only set to true when you specifically need additional fields not included in the default response, as it can significantly increase response size.", "title": "Details", "type": "boolean"}, "method": {"default": "GET", "description": "HTTP method to use. Usually GET, sometimes POST, PATCH, or PUT. DELETE is not allowed for safety reasons.", "enum": ["GET", "POST", "PATCH", "PUT"], "title": "Method", "type": "string"}, "path": {"description": "GitHub API path.\nExample paths:\n- /repos/{owner}/{repo}/issues\nAlways use q=is:pr or q=is:issue to filter for issues or PRs.\nUse creator={username} or assignee={username} to filter by specific user.\nThis is the most efficient way to search when the user is asking about \"my PRs\" or \"my issues\" in the current repo.\n\n- /repos/{owner}/{repo}/pulls\nAlways create and push the new branch and check that all requirements are fulfilled before creating a PR.\nIf you do not know the convention of the repo for branch names and which branch to set as target, ask the user and memorize the answers.\nUse `head=user:ref-name` or `head=organization:ref-name` to filter PRs by specific branch. Example: `head=octocat:my-feature-branch`.\n\n- /orgs/{org}/issues\nList issues and PRs related to a user or in an org respectively.\nAlways use the `filter` parameter to specify a subset - filter=assigned is the default, but it could be created, mentioned, subscribed, repos, all.\nValues assigned, created, mentioned, subscribed describe relationship to the user.\nfilter=all returns all issues and PRs the user has access to.\n\n- /issues\nUse this endpoints to get the authenticated user's assigned issues or PRs across all repositories.\nAlways use the `filter` parameter to specify a subset - filter=assigned is the default, but it could be created, mentioned, subscribed, repos, all.\n\n- /search/issues\nUse this when you need to find issues or PRs with specific criteria.\nFor the current authenticated user, always use `author:@me` or `assignee:@me` in the query.\nFor other users, use `author:{username}` or `assignee:{username}`.\nAlways use q=is:pr or q=is:issue to filter between PRs and issues.\n\n- /search/code\nUse this to find code with specific content.\n\n- /search/commits\nUse this to find commits with specific criteria.\nTo filter by author, use `author:{username}` or `committer:{username}`.\n\n- /repos/{owner}/{repo}/commits/{commit_sha}/check-runs\n- /repos/{owner}/{repo}/commits/{commit_sha}/status\nCheck CI status.  You should verify status when you push a commit to a PR.\nNot all PR checks are Check Runs, so the /status covers more CIs, but /check-runs provides more detailed information.\n\nA few other examples\n- /repos/{owner}/{repo}/actions/runs\n- /repos/{owner}/{repo}/pulls/{number}/files\n- /repos/{owner}/{repo}/releases/{release_id}/reactions\n- /repos/{owner}/{repo}/actions/jobs/{job_id}\n...\n\nThere are no paths for wikis and discussions. They need to be accessed through web fetch instead.\n", "title": "Path", "type": "string"}, "summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "A short human-readable summary of what this API call will do. This helps users understand the purpose of the call.", "title": "Summary"}}, "required": ["path"], "title": "GitHubAPIInput", "type": "object"}}, {"description": "Fetches data from a webpage and converts it into Markdown.\n\n1. The tool takes in a URL and returns the content of the page in Markdown format;\n2. If the return is not valid Markdown, it means the tool cannot successfully parse this page.", "name": "web-fetch", "parameters": {"properties": {"url": {"description": "The URL to fetch.", "type": "string"}}, "required": ["url"], "type": "object"}}, {"description": "This tool is Augment's context engine, the world's best codebase context engine. It:\n1. Takes in a natural language description of the code you are looking for;\n2. Uses a proprietary retrieval/embedding model suite that produces the highest-quality recall of relevant code snippets from across the codebase;\n3. Maintains a real-time index of the codebase, so the results are always up-to-date and reflects the current state of the codebase;\n4. Can retrieve across different programming languages;\n5. Only reflects the current state of the codebase on the disk, and has no information on version control or code history.", "name": "codebase-retrieval", "parameters": {"properties": {"information_request": {"description": "A description of the information you need.", "type": "string"}}, "required": ["information_request"], "type": "object"}}, {"description": "Remove files. ONLY use this tool to delete files in the user's workspace. This is the only safe tool to delete files in a way that the user can undo the change. Do NOT use the shell or launch-process tools to remove files.", "name": "remove-files", "parameters": {"properties": {"file_paths": {"description": "The paths of the files to remove.", "items": {"type": "string"}, "type": "array"}}, "required": ["file_paths"], "type": "object"}}, {"description": "Save a new file. Use this tool to write new files with the attached content. Generate `instructions_reminder` first to remind yourself to limit the file content to at most 300 lines. It CANNOT modify existing files. Do NOT use this tool to edit an existing file by overwriting it entirely. Use the str-replace-editor tool to edit existing files instead.", "name": "save-file", "parameters": {"properties": {"add_last_line_newline": {"description": "Whether to add a newline at the end of the file (default: true).", "type": "boolean"}, "file_content": {"description": "The content of the file.", "type": "string"}, "instructions_reminder": {"description": "Should be exactly this string: 'LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.'", "type": "string"}, "path": {"description": "The path of the file to save.", "type": "string"}}, "required": ["instructions_reminder", "path", "file_content"], "type": "object"}}, {"description": "View the current task list for the conversation.", "name": "view_tasklist", "parameters": {"properties": {}, "required": [], "type": "object"}}, {"description": "Reorganize the task list structure for the current conversation. Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool.", "name": "reorganize_tasklist", "parameters": {"properties": {"markdown": {"description": "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a UUID of 'NEW_UUID'. Must contain exactly one root task with proper hierarchy using dash indentation.", "type": "string"}}, "required": ["markdown"], "type": "object"}}, {"description": "Update one or more tasks' properties (state, name, description). Can update a single task or multiple tasks in one call. Use this on complex sequences of work to plan, track progress, and manage work.", "name": "update_tasks", "parameters": {"properties": {"tasks": {"description": "Array of tasks to update. Each task should have a task_id and the properties to update.", "items": {"properties": {"description": {"description": "New task description.", "type": "string"}, "name": {"description": "New task name.", "type": "string"}, "state": {"description": "New task state. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x].", "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"], "type": "string"}, "task_id": {"description": "The UUID of the task to update.", "type": "string"}}, "required": ["task_id"], "type": "object"}, "type": "array"}}, "required": ["tasks"], "type": "object"}}, {"description": "Add one or more new tasks to the task list. Can add a single task or multiple tasks in one call. Tasks can be added as subtasks or after specific tasks. Use this when planning complex sequences of work.", "name": "add_tasks", "parameters": {"properties": {"tasks": {"description": "Array of tasks to create. Each task should have name and description.", "items": {"properties": {"after_task_id": {"description": "UUID of the task after which this task should be inserted.", "type": "string"}, "description": {"description": "The description of the new task.", "type": "string"}, "name": {"description": "The name of the new task.", "type": "string"}, "parent_task_id": {"description": "UUID of the parent task if this should be a subtask.", "type": "string"}, "state": {"description": "Initial state of the task. Defaults to NOT_STARTED.", "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"], "type": "string"}}, "required": ["name", "description"], "type": "object"}, "type": "array"}}, "required": ["tasks"], "type": "object"}}, {"description": "Call this tool when user asks you:\n- to remember something\n- to create memory/memories\n\nUse this tool only with information that can be useful in the long-term.\nDo not use this tool for temporary information.\n", "name": "remember", "parameters": {"properties": {"memory": {"description": "The concise (1 sentence) memory to remember.", "type": "string"}}, "required": ["memory"], "type": "object"}}, {"description": "Render a Mermaid diagram from the provided definition. This tool takes Mermaid diagram code and renders it as an interactive diagram with pan/zoom controls and copy functionality.", "name": "render-mermaid", "parameters": {"properties": {"diagram_definition": {"description": "The Mermaid diagram definition code to render", "type": "string"}, "title": {"default": "Mermaid Diagram", "description": "Optional title for the diagram", "type": "string"}}, "required": ["diagram_definition"], "type": "object"}}, {"description": "View a specific range of lines from untruncated content", "name": "view-range-untruncated", "parameters": {"properties": {"end_line": {"description": "The ending line number (1-based, inclusive)", "type": "integer"}, "reference_id": {"description": "The reference ID of the truncated content (found in the truncation footer)", "type": "string"}, "start_line": {"description": "The starting line number (1-based, inclusive)", "type": "integer"}}, "required": ["reference_id", "start_line", "end_line"], "type": "object"}}, {"description": "Search for a term within untruncated content", "name": "search-untruncated", "parameters": {"properties": {"context_lines": {"description": "Number of context lines to include before and after matches (default: 2)", "type": "integer"}, "reference_id": {"description": "The reference ID of the truncated content (found in the truncation footer)", "type": "string"}, "search_term": {"description": "The term to search for within the content", "type": "string"}}, "required": ["reference_id", "search_term"], "type": "object"}}, {"description": "Custom tool for viewing files and directories and searching within files with regex query\n* `path` is a file or directory path relative to the workspace root\n* For files: displays the result of applying `cat -n` to the file\n* For directories: lists files and subdirectories up to 2 levels deep\n* If the output is long, it will be truncated and marked with `<response clipped>`\n\nRegex search (for files only):\n* Use `search_query_regex` to search for patterns in the file using regular expressions\n* Use `case_sensitive` parameter to control case sensitivity (default: false)\n* When using regex search, only matching lines and their context will be shown\n* Use `context_lines_before` and `context_lines_after` to control how many lines of context to show (default: 5)\n* Non-matching sections between matches are replaced with `...`\n* If `view_range` is also specified, the search is limited to that range\n\nUse the following regex syntax for `search_query_regex`:\n\n# Regex Syntax Reference\n\nOnly the core regex feature common across JavaScript and Rust are supported.\n\n## Supported regex syntax\n\n* **Escaping** - Escape metacharacters with a backslash: `\\.` `\\+` `\\?` `\\*` `\\|` `\\(` `\\)` `\\[`.\n* **Dot** `.` - matches any character **except newline** (`\\n`, `\\r`, `\\u2028`, `\\u2029`).\n* **Character classes** - `[abc]`, ranges such as `[a-z]`, and negation `[^…]`. Use explicit ASCII ranges; avoid shorthand like `\\d`.\n* **Alternation** - `foo|bar` chooses the leftmost successful branch.\n* **Quantifiers** - `*`, `+`, `?`, `{n}`, `{n,}`, `{n,m}` (greedy). Add `?` after any of these for the lazy version.\n* **Anchors** - `^` (start of line), `$` (end of line).\n* **Special characters** - Use `\\t` for tab character\n\n---\n\n## Do **Not** Use (Unsupported)\n\n* Newline character `\\n`. Only single line mode is supported.\n* Look-ahead / look-behind `(?= … )`, `(?<= … )`.\n* Back-references `\\1`, `\\k<name>`.\n* Groups `(?<name> … )`, `(?P<name> … )`.\n* Shorthand classes `\\d`, `\\s`, `\\w`, `\\b`, Unicode property escapes `\\p{…}`.\n* Flags inside pattern `(?i)`, `(?m)`, etc.\n* Recursion, conditionals, atomic groups, possessive quantifiers\n* Unicode escapes like these `\\u{1F60A}` or `\\u1F60A`.\n\n\nNotes for using the tool:\n* Strongly prefer to use `search_query_regex` instead of `view_range` when looking for a specific symbol in the file.\n* Use the `view_range` parameter to specify a range of lines to view, e.g. [501, 1000] will show lines from 501 to 1000\n* Indices are 1-based and inclusive\n* Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file\n* The `view_range` and `search_query_regex` parameters are only applicable when viewing files, not directories\n", "name": "view", "parameters": {"properties": {"case_sensitive": {"default": false, "description": "Whether the regex search should be case-sensitive. Only used when search_query_regex is specified. Default: false (case-insensitive).", "type": "boolean"}, "context_lines_after": {"default": 5, "description": "Number of lines to show after each regex match. Only used when search_query_regex is specified. Default: 5.", "type": "integer"}, "context_lines_before": {"default": 5, "description": "Number of lines to show before each regex match. Only used when search_query_regex is specified. Default: 5.", "type": "integer"}, "path": {"description": "Full path to file or directory relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.", "type": "string"}, "search_query_regex": {"description": "Optional parameter for files only. The regex pattern to search for. Only use core regex syntax common to JavaScript and Rust. See the regex syntax guide in the tool description. When specified, only lines matching the pattern (plus context lines) will be shown. Non-matching sections are replaced with '...'.", "type": "string"}, "type": {"description": "Type of path to view. Allowed options are: 'file', 'directory'.", "enum": ["file", "directory"], "type": "string"}, "view_range": {"description": "Optional parameter when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.", "items": {"type": "integer"}, "type": "array"}}, "required": ["path", "type"], "type": "object"}}]